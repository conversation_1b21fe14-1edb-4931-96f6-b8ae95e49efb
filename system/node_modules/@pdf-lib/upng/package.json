{"name": "@pdf-lib/upng", "license": "MIT", "version": "1.0.1", "description": "Small, fast and advanced PNG / APNG encoder and decoder", "main": "cjs/UPNG.js", "unpkg": "dist/UPNG.min.js", "module": "UPNG.js", "types": "index.d.ts", "files": ["cjs/", "dist/", "UPNG.js", "index.d.ts", "LICENSE.md", "package.json", "README.md", "yarn.lock"], "scripts": {"build": "yarn build:cjs && yarn build:umd && yarn build:umd:min", "build:cjs": "babel --plugins @babel/plugin-transform-modules-commonjs --out-dir cjs UPNG.js", "build:umd": "rollup --config rollup.config.js --file dist/UPNG.js", "build:umd:min": "rollup --config rollup.config.js --file dist/UPNG.min.js --environment MINIFY"}, "repository": {"type": "git", "url": "https://github.com/Hopding/upng"}, "author": "photopea (https://github.com/photopea)", "contributors": ["<PERSON> (https://github.com/Hopding)", "Scimonster (https://github.com/Scimonster)"], "bugs": "https://github.com/Hopding/upng/issues", "keywords": ["png", "apng", "image", "conversion"], "dependencies": {"pako": "^1.0.10"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.8.4", "@babel/plugin-transform-modules-commonjs": "^7.8.3", "@rollup/plugin-commonjs": "^11.0.0", "@rollup/plugin-node-resolve": "^7.1.1", "rollup": "^1.31.1", "rollup-plugin-terser": "^5.1.3"}}