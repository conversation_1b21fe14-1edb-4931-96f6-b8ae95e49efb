{"version": 3, "file": "ErrorResponse.js", "sourceRoot": "", "sources": ["../../../src/protocol/ErrorResponse.ts"], "names": [], "mappings": ";;;AAmBA,MAAa,SAAS;IAEX;IACA;IACA;IAHT,YACS,KAAgB,EAChB,OAAe,EACf,UAAmB;QAFnB,UAAK,GAAL,KAAK,CAAW;QAChB,YAAO,GAAP,OAAO,CAAQ;QACf,eAAU,GAAV,UAAU,CAAS;IACzB,CAAC;IAEJ,eAAe,CAAC,SAAiB;QAC/B,OAAO;YACL,IAAI,EAAE,OAAO;YACb,EAAE,EAAE,SAAS;YACb,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,UAAU,EAAE,IAAI,CAAC,UAAU;SAC5B,CAAC;IACJ,CAAC;CACF;AAhBD,8BAgBC;AAED,MAAa,wBAAyB,SAAQ,SAAS;IACrD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,qDAA4B,OAAO,EAAE,UAAU,CAAC,CAAC;IACxD,CAAC;CACF;AAJD,4DAIC;AAED,MAAa,yBAA0B,SAAQ,SAAS;IACtD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,wDAA6B,OAAO,EAAE,UAAU,CAAC,CAAC;IACzD,CAAC;CACF;AAJD,8DAIC;AAED,MAAa,8BAA+B,SAAQ,SAAS;IAC3D,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,oEAAkC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC9D,CAAC;CACF;AAJD,wEAIC;AAED,MAAa,oBAAqB,SAAQ,SAAS;IACjD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,8CAAwB,OAAO,EAAE,UAAU,CAAC,CAAC;IACpD,CAAC;CACF;AAJD,oDAIC;AAED,MAAa,sBAAuB,SAAQ,SAAS;IACnD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,kDAA0B,OAAO,EAAE,UAAU,CAAC,CAAC;IACtD,CAAC;CACF;AAJD,wDAIC;AAED,MAAa,oBAAqB,SAAQ,SAAS;IACjD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,8CAAwB,OAAO,EAAE,UAAU,CAAC,CAAC;IACpD,CAAC;CACF;AAJD,oDAIC;AAED,MAAa,qBAAsB,SAAQ,SAAS;IAClD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,gDAAyB,OAAO,EAAE,UAAU,CAAC,CAAC;IACrD,CAAC;CACF;AAJD,sDAIC;AAED,MAAa,2BAA4B,SAAQ,SAAS;IACxD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,6DAA+B,OAAO,EAAE,UAAU,CAAC,CAAC;IAC3D,CAAC;CACF;AAJD,kEAIC;AAED,MAAa,wBAAyB,SAAQ,SAAS;IACrD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,sDAA4B,OAAO,EAAE,UAAU,CAAC,CAAC;IACxD,CAAC;CACF;AAJD,4DAIC;AAED,MAAa,mBAAoB,SAAQ,SAAS;IAChD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,4CAAuB,OAAO,EAAE,UAAU,CAAC,CAAC;IACnD,CAAC;CACF;AAJD,kDAIC;AAED,MAAa,sBAAuB,SAAQ,SAAS;IACnD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,kDAA0B,OAAO,EAAE,UAAU,CAAC,CAAC;IACtD,CAAC;CACF;AAJD,wDAIC;AAED,MAAa,qBAAsB,SAAQ,SAAS;IAClD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,gDAAyB,OAAO,EAAE,UAAU,CAAC,CAAC;IACrD,CAAC;CACF;AAJD,sDAIC;AAED,MAAa,0BAA2B,SAAQ,SAAS;IACvD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,2DAA8B,OAAO,EAAE,UAAU,CAAC,CAAC;IAC1D,CAAC;CACF;AAJD,gEAIC;AAED,MAAa,0BAA2B,SAAQ,SAAS;IACvD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,0DAA8B,OAAO,EAAE,UAAU,CAAC,CAAC;IAC1D,CAAC;CACF;AAJD,gEAIC;AAED,MAAa,uBAAwB,SAAQ,SAAS;IACpD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,mDAA2B,OAAO,EAAE,UAAU,CAAC,CAAC;IACvD,CAAC;CACF;AAJD,0DAIC;AAED,MAAa,qBAAsB,SAAQ,SAAS;IAClD,YAAY,OAAe,EAAE,UAAU,GAAG,IAAI,KAAK,EAAE,CAAC,KAAK;QACzD,KAAK,+CAAyB,OAAO,EAAE,UAAU,CAAC,CAAC;IACrD,CAAC;CACF;AAJD,sDAIC;AAED,MAAa,8BAA+B,SAAQ,SAAS;IAC3D,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,mEAAkC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC9D,CAAC;CACF;AAJD,wEAIC;AAED,MAAa,6BAA8B,SAAQ,SAAS;IAC1D,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,iEAAiC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC7D,CAAC;CACF;AAJD,sEAIC;AAED,MAAa,6BAA8B,SAAQ,SAAS;IAC1D,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,+DAAiC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC7D,CAAC;CACF;AAJD,sEAIC;AAED,MAAa,+BAAgC,SAAQ,SAAS;IAC5D,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,qEAAmC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC/D,CAAC;CACF;AAJD,0EAIC;AAED,MAAa,0BAA2B,SAAQ,SAAS;IACvD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,2DAA8B,OAAO,EAAE,UAAU,CAAC,CAAC;IAC1D,CAAC;CACF;AAJD,gEAIC;AAED,MAAa,6BAA8B,SAAQ,SAAS;IAC1D,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,kEAAiC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC7D,CAAC;CACF;AAJD,sEAIC;AAED,MAAa,uCAAwC,SAAQ,SAAS;IACpE,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,oFAA2C,OAAO,EAAE,UAAU,CAAC,CAAC;IACvE,CAAC;CACF;AAJD,0FAIC"}