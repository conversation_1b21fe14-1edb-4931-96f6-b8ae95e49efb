{"version": 3, "file": "neuquant.d.ts", "sourceRoot": "", "sources": ["../../../../../src/palette/neuquant/neuquant.ts"], "names": [], "mappings": "AA+BA,OAAO,EAAE,cAAc,EAAE,MAAM,4BAA4B,CAAC;AAC5D,OAAO,EAAE,0BAA0B,EAAE,MAAM,mCAAmC,CAAC;AAC/E,OAAO,EAAE,wBAAwB,EAAE,MAAM,qBAAqB,CAAC;AAC/D,OAAO,EAAE,0BAA0B,EAAE,MAAM,+BAA+B,CAAC;AAyD3E,qBAAa,QAAS,SAAQ,wBAAwB;IAKpD,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAO;IACtC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAO;IACtC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAO;IACtC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAO;IACtC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAoB;IAG5D,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAO;IAGvC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAM;IAG/C,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAmC;IACvE,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAM;IAKzC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAM;IACxC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAgD;IAG7E,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CACsC;IAKxE,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAK;IAG7C,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAkC;IAGrE,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAM;IAK7C,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAM;IAG7C,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAiC;IAGnE,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAK;IAC1C,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAA+B;IAC/D,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,kBAAkB,CACU;IACpD,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAoC;IAEzE,OAAO,CAAC,WAAW,CAAU;IAC7B,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAS;IACtC,OAAO,CAAC,QAAQ,CAAY;IAE5B,4BAA4B;IAC5B,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAU;IACxC,OAAO,CAAC,SAAS,CAAY;IAG7B,OAAO,CAAC,KAAK,CAAY;IAGzB,OAAO,CAAC,KAAK,CAAY;IACzB,OAAO,CAAC,QAAQ,CAAC,SAAS,CAA6B;gBAGrD,uBAAuB,EAAE,0BAA0B,EACnD,MAAM,SAAM;IAgBd,MAAM,CAAC,cAAc,EAAE,cAAc;IAIpC,QAAQ,IAAI,gBAAgB,CAAC,0BAA0B,CAAC;IAWzD,OAAO,CAAC,KAAK;IAgBb;;OAEG;IACH,OAAO,CAAE,MAAM;IA2Ef,OAAO,CAAC,aAAa;IAWrB;;OAEG;IACH,OAAO,CAAC,eAAe;IAgCvB;;OAEG;IACH,OAAO,CAAC,YAAY;IAoBpB;;;;;;;;;;OAUG;IACH,OAAO,CAAC,QAAQ;CAkCjB"}