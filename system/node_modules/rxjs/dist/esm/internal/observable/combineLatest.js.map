{"version": 3, "file": "combineLatest.js", "sourceRoot": "", "sources": ["../../../../src/internal/observable/combineLatest.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAE3C,OAAO,EAAE,oBAAoB,EAAE,MAAM,8BAA8B,CAAC;AAEpE,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AAC9B,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAE5C,OAAO,EAAE,gBAAgB,EAAE,MAAM,0BAA0B,CAAC;AAC5D,OAAO,EAAE,iBAAiB,EAAE,YAAY,EAAE,MAAM,cAAc,CAAC;AAC/D,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AACpD,OAAO,EAAE,wBAAwB,EAAE,MAAM,iCAAiC,CAAC;AAE3E,OAAO,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAC;AAwL1D,MAAM,UAAU,aAAa,CAAoC,GAAG,IAAW;IAC7E,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;IACrC,MAAM,cAAc,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAE/C,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC;IAE/D,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;QAI5B,OAAO,IAAI,CAAC,EAAE,EAAE,SAAgB,CAAC,CAAC;KACnC;IAED,MAAM,MAAM,GAAG,IAAI,UAAU,CAC3B,iBAAiB,CACf,WAAoD,EACpD,SAAS,EACT,IAAI;QACF,CAAC;YACC,CAAC,MAAM,EAAE,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC;QACxC,CAAC;YACC,QAAQ,CACb,CACF,CAAC;IAEF,OAAO,cAAc,CAAC,CAAC,CAAE,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAmB,CAAC,CAAC,CAAC,MAAM,CAAC;AACpG,CAAC;AAED,MAAM,UAAU,iBAAiB,CAC/B,WAAmC,EACnC,SAAyB,EACzB,iBAAyC,QAAQ;IAEjD,OAAO,CAAC,UAA2B,EAAE,EAAE;QAGrC,aAAa,CACX,SAAS,EACT,GAAG,EAAE;YACH,MAAM,EAAE,MAAM,EAAE,GAAG,WAAW,CAAC;YAE/B,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;YAGjC,IAAI,MAAM,GAAG,MAAM,CAAC;YAIpB,IAAI,oBAAoB,GAAG,MAAM,CAAC;YAGlC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC/B,aAAa,CACX,SAAS,EACT,GAAG,EAAE;oBACH,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,SAAgB,CAAC,CAAC;oBACtD,IAAI,aAAa,GAAG,KAAK,CAAC;oBAC1B,MAAM,CAAC,SAAS,CACd,wBAAwB,CACtB,UAAU,EACV,CAAC,KAAK,EAAE,EAAE;wBAER,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;wBAClB,IAAI,CAAC,aAAa,EAAE;4BAElB,aAAa,GAAG,IAAI,CAAC;4BACrB,oBAAoB,EAAE,CAAC;yBACxB;wBACD,IAAI,CAAC,oBAAoB,EAAE;4BAGzB,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;yBACjD;oBACH,CAAC,EACD,GAAG,EAAE;wBACH,IAAI,CAAC,EAAE,MAAM,EAAE;4BAGb,UAAU,CAAC,QAAQ,EAAE,CAAC;yBACvB;oBACH,CAAC,CACF,CACF,CAAC;gBACJ,CAAC,EACD,UAAU,CACX,CAAC;aACH;QACH,CAAC,EACD,UAAU,CACX,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AAMD,SAAS,aAAa,CAAC,SAAoC,EAAE,OAAmB,EAAE,YAA0B;IAC1G,IAAI,SAAS,EAAE;QACb,eAAe,CAAC,YAAY,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;KACnD;SAAM;QACL,OAAO,EAAE,CAAC;KACX;AACH,CAAC"}