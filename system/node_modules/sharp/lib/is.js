// Copyright 2013 <PERSON><PERSON> and others.
// SPDX-License-Identifier: Apache-2.0

'use strict';

/**
 * Is this value defined and not null?
 * @private
 */
const defined = function (val) {
  return typeof val !== 'undefined' && val !== null;
};

/**
 * Is this value an object?
 * @private
 */
const object = function (val) {
  return typeof val === 'object';
};

/**
 * Is this value a plain object?
 * @private
 */
const plainObject = function (val) {
  return Object.prototype.toString.call(val) === '[object Object]';
};

/**
 * Is this value a function?
 * @private
 */
const fn = function (val) {
  return typeof val === 'function';
};

/**
 * Is this value a boolean?
 * @private
 */
const bool = function (val) {
  return typeof val === 'boolean';
};

/**
 * Is this value a Buffer object?
 * @private
 */
const buffer = function (val) {
  return val instanceof Buffer;
};

/**
 * Is this value a typed array object?. E.g. Uint8Array or Uint8ClampedArray?
 * @private
 */
const typedArray = function (val) {
  if (defined(val)) {
    switch (val.constructor) {
      case Uint8Array:
      case Uint8ClampedArray:
      case Int8Array:
      case Uint16Array:
      case Int16Array:
      case Uint32Array:
      case Int32Array:
      case Float32Array:
      case Float64Array:
        return true;
    }
  }

  return false;
};

/**
 * Is this value an ArrayBuffer object?
 * @private
 */
const arrayBuffer = function (val) {
  return val instanceof ArrayBuffer;
};

/**
 * Is this value a non-empty string?
 * @private
 */
const string = function (val) {
  return typeof val === 'string' && val.length > 0;
};

/**
 * Is this value a real number?
 * @private
 */
const number = function (val) {
  return typeof val === 'number' && !Number.isNaN(val);
};

/**
 * Is this value an integer?
 * @private
 */
const integer = function (val) {
  return Number.isInteger(val);
};

/**
 * Is this value within an inclusive given range?
 * @private
 */
const inRange = function (val, min, max) {
  return val >= min && val <= max;
};

/**
 * Is this value within the elements of an array?
 * @private
 */
const inArray = function (val, list) {
  return list.includes(val);
};

/**
 * Create an Error with a message relating to an invalid parameter.
 *
 * @param {string} name - parameter name.
 * @param {string} expected - description of the type/value/range expected.
 * @param {*} actual - the value received.
 * @returns {Error} Containing the formatted message.
 * @private
 */
const invalidParameterError = function (name, expected, actual) {
  return new Error(
    `Expected ${expected} for ${name} but received ${actual} of type ${typeof actual}`
  );
};

module.exports = {
  defined: defined,
  object: object,
  plainObject: plainObject,
  fn: fn,
  bool: bool,
  buffer: buffer,
  typedArray: typedArray,
  arrayBuffer: arrayBuffer,
  string: string,
  number: number,
  integer: integer,
  inRange: inRange,
  inArray: inArray,
  invalidParameterError: invalidParameterError
};
