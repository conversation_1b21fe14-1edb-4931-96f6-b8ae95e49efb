/*
 * HEIF codec.
 * Copyright (c) 2017 struktur AG, <PERSON> <<EMAIL>>
 *
 * This file is part of libheif.
 *
 * libheif is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License as
 * published by the Free Software Foundation, either version 3 of
 * the License, or (at your option) any later version.
 *
 * libheif is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with libheif.  If not, see <http://www.gnu.org/licenses/>.
 */

/* heif_version.h
 *
 * This file was automatically generated when libheif was built.
 *
 * DO NOT EDIT THIS FILE.
 */
#ifndef LIBHEIF_HEIF_VERSION_H
#define LIBHEIF_HEIF_VERSION_H

/* Numeric representation of the version */
#define LIBHEIF_NUMERIC_VERSION ((1<<24) | (16<<16) | (2<<8) | 0)

/* Version string */
#define LIBHEIF_VERSION "1.16.2"

#define LIBHEIF_PLUGIN_DIRECTORY "/data/mxe/usr/x86_64-w64-mingw32.static.posix.web/lib/libheif"

#endif  // LIBHEIF_HEIF_VERSION_H
