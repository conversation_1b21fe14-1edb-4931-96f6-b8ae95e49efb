
/* autogenerated by generate-emulation */

#ifndef _ORC_EMULATE_OPCODES_H_
#define _ORC_EMULATE_OPCODES_H_

void emulate_absb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_addb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_addssb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_addusb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_andb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_andnb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_avgsb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_avgub (OrcOpcodeExecutor *ex, int i, int n);
void emulate_cmpeqb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_cmpgtsb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_copyb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_loadb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_loadoffb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_loadupdb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_loadupib (OrcOpcodeExecutor *ex, int i, int n);
void emulate_loadpb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_ldresnearb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_ldresnearl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_ldreslinb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_ldreslinl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_maxsb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_maxub (OrcOpcodeExecutor *ex, int i, int n);
void emulate_minsb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_minub (OrcOpcodeExecutor *ex, int i, int n);
void emulate_mullb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_mulhsb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_mulhub (OrcOpcodeExecutor *ex, int i, int n);
void emulate_orb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_shlb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_shrsb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_shrub (OrcOpcodeExecutor *ex, int i, int n);
void emulate_signb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_storeb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_subb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_subssb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_subusb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_xorb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_absw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_addw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_addssw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_addusw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_andw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_andnw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_avgsw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_avguw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_cmpeqw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_cmpgtsw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_copyw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_div255w (OrcOpcodeExecutor *ex, int i, int n);
void emulate_divluw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_loadw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_loadoffw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_loadpw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_maxsw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_maxuw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_minsw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_minuw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_mullw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_mulhsw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_mulhuw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_orw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_shlw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_shrsw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_shruw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_signw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_storew (OrcOpcodeExecutor *ex, int i, int n);
void emulate_subw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_subssw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_subusw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_xorw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_absl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_addl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_addssl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_addusl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_andl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_andnl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_avgsl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_avgul (OrcOpcodeExecutor *ex, int i, int n);
void emulate_cmpeql (OrcOpcodeExecutor *ex, int i, int n);
void emulate_cmpgtsl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_copyl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_loadl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_loadoffl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_loadpl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_maxsl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_maxul (OrcOpcodeExecutor *ex, int i, int n);
void emulate_minsl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_minul (OrcOpcodeExecutor *ex, int i, int n);
void emulate_mulll (OrcOpcodeExecutor *ex, int i, int n);
void emulate_mulhsl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_mulhul (OrcOpcodeExecutor *ex, int i, int n);
void emulate_orl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_shll (OrcOpcodeExecutor *ex, int i, int n);
void emulate_shrsl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_shrul (OrcOpcodeExecutor *ex, int i, int n);
void emulate_signl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_storel (OrcOpcodeExecutor *ex, int i, int n);
void emulate_subl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_subssl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_subusl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_xorl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_loadq (OrcOpcodeExecutor *ex, int i, int n);
void emulate_loadpq (OrcOpcodeExecutor *ex, int i, int n);
void emulate_storeq (OrcOpcodeExecutor *ex, int i, int n);
void emulate_splatw3q (OrcOpcodeExecutor *ex, int i, int n);
void emulate_copyq (OrcOpcodeExecutor *ex, int i, int n);
void emulate_cmpeqq (OrcOpcodeExecutor *ex, int i, int n);
void emulate_cmpgtsq (OrcOpcodeExecutor *ex, int i, int n);
void emulate_andq (OrcOpcodeExecutor *ex, int i, int n);
void emulate_andnq (OrcOpcodeExecutor *ex, int i, int n);
void emulate_orq (OrcOpcodeExecutor *ex, int i, int n);
void emulate_xorq (OrcOpcodeExecutor *ex, int i, int n);
void emulate_addq (OrcOpcodeExecutor *ex, int i, int n);
void emulate_subq (OrcOpcodeExecutor *ex, int i, int n);
void emulate_shlq (OrcOpcodeExecutor *ex, int i, int n);
void emulate_shrsq (OrcOpcodeExecutor *ex, int i, int n);
void emulate_shruq (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convsbw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convubw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_splatbw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_splatbl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convswl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convuwl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convslq (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convulq (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convwb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convhwb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convssswb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convsuswb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convusswb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convuuswb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convlw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convhlw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convssslw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convsuslw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convusslw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convuuslw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convql (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convsssql (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convsusql (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convussql (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convuusql (OrcOpcodeExecutor *ex, int i, int n);
void emulate_mulsbw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_mulubw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_mulswl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_muluwl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_mulslq (OrcOpcodeExecutor *ex, int i, int n);
void emulate_mululq (OrcOpcodeExecutor *ex, int i, int n);
void emulate_accw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_accl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_accsadubl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_swapw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_swapl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_swapwl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_swapq (OrcOpcodeExecutor *ex, int i, int n);
void emulate_swaplq (OrcOpcodeExecutor *ex, int i, int n);
void emulate_select0wb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_select1wb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_select0lw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_select1lw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_select0ql (OrcOpcodeExecutor *ex, int i, int n);
void emulate_select1ql (OrcOpcodeExecutor *ex, int i, int n);
void emulate_mergelq (OrcOpcodeExecutor *ex, int i, int n);
void emulate_mergewl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_mergebw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_splitql (OrcOpcodeExecutor *ex, int i, int n);
void emulate_splitlw (OrcOpcodeExecutor *ex, int i, int n);
void emulate_splitwb (OrcOpcodeExecutor *ex, int i, int n);
void emulate_addf (OrcOpcodeExecutor *ex, int i, int n);
void emulate_subf (OrcOpcodeExecutor *ex, int i, int n);
void emulate_mulf (OrcOpcodeExecutor *ex, int i, int n);
void emulate_divf (OrcOpcodeExecutor *ex, int i, int n);
void emulate_sqrtf (OrcOpcodeExecutor *ex, int i, int n);
void emulate_maxf (OrcOpcodeExecutor *ex, int i, int n);
void emulate_minf (OrcOpcodeExecutor *ex, int i, int n);
void emulate_cmpeqf (OrcOpcodeExecutor *ex, int i, int n);
void emulate_cmpltf (OrcOpcodeExecutor *ex, int i, int n);
void emulate_cmplef (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convfl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convlf (OrcOpcodeExecutor *ex, int i, int n);
void emulate_addd (OrcOpcodeExecutor *ex, int i, int n);
void emulate_subd (OrcOpcodeExecutor *ex, int i, int n);
void emulate_muld (OrcOpcodeExecutor *ex, int i, int n);
void emulate_divd (OrcOpcodeExecutor *ex, int i, int n);
void emulate_sqrtd (OrcOpcodeExecutor *ex, int i, int n);
void emulate_maxd (OrcOpcodeExecutor *ex, int i, int n);
void emulate_mind (OrcOpcodeExecutor *ex, int i, int n);
void emulate_cmpeqd (OrcOpcodeExecutor *ex, int i, int n);
void emulate_cmpltd (OrcOpcodeExecutor *ex, int i, int n);
void emulate_cmpled (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convdl (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convld (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convfd (OrcOpcodeExecutor *ex, int i, int n);
void emulate_convdf (OrcOpcodeExecutor *ex, int i, int n);

#endif

